"""
Enhanced OTE Dashboard with Performance Optimizations
- Async data fetching with connection pooling
- Redis caching for better performance
- Background task processing
- Error handling and recovery
- Optimized for Raspberry Pi deployment
"""

import asyncio
import aiohttp
import redis
import json
import logging
import os
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor
import psutil

from flask import Flask, render_template, jsonify, request
from flask_cors import CORS
from flask_caching import Cache
from bs4 import BeautifulSoup
import re

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration
@dataclass
class Config:
    REDIS_URL: str = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
    CACHE_TIMEOUT: int = int(os.getenv('CACHE_TIMEOUT', '900'))  # 15 minutes
    REQUEST_TIMEOUT: int = int(os.getenv('REQUEST_TIMEOUT', '10'))
    MAX_CONNECTIONS: int = int(os.getenv('MAX_CONNECTIONS', '20'))
    BACKGROUND_UPDATE_INTERVAL: int = int(os.getenv('UPDATE_INTERVAL', '300'))  # 5 minutes
    DEBUG: bool = os.getenv('DEBUG', 'False').lower() == 'true'

config = Config()

# Flask app setup
app = Flask(__name__, 
           static_folder='static',
           template_folder='templates')
CORS(app)

# Enhanced caching with Redis fallback
try:
    redis_client = redis.from_url(config.REDIS_URL, decode_responses=True)
    redis_client.ping()  # Test connection
    cache_config = {
        'CACHE_TYPE': 'RedisCache',
        'CACHE_REDIS_URL': config.REDIS_URL,
        'CACHE_DEFAULT_TIMEOUT': config.CACHE_TIMEOUT
    }
    logger.info("Redis cache initialized successfully")
except Exception as e:
    logger.warning(f"Redis not available, falling back to SimpleCache: {e}")
    redis_client = None
    cache_config = {
        'CACHE_TYPE': 'SimpleCache',
        'CACHE_DEFAULT_TIMEOUT': config.CACHE_TIMEOUT
    }

cache = Cache(app, config=cache_config)

# Data models
@dataclass
class MarketData:
    tab: str
    date: str
    title: str = ""
    commodity: str = ""
    price: str = ""
    change: str = ""
    change_direction: Optional[str] = None
    amount: str = ""
    currency: str = ""
    is_special: bool = False
    is_system_deviation: bool = False
    min_value: str = ""
    max_value: str = ""
    timestamp: datetime = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

class PerformanceMonitor:
    """Monitor system performance for Raspberry Pi optimization"""
    
    @staticmethod
    def get_system_stats() -> Dict[str, Any]:
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            return {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_available_mb': memory.available // (1024 * 1024),
                'disk_percent': disk.percent,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error getting system stats: {e}")
            return {}

    @staticmethod
    def is_system_healthy() -> bool:
        """Check if system resources are within acceptable limits"""
        try:
            stats = PerformanceMonitor.get_system_stats()
            return (
                stats.get('cpu_percent', 100) < 80 and
                stats.get('memory_percent', 100) < 85 and
                stats.get('disk_percent', 100) < 90
            )
        except:
            return True  # Assume healthy if we can't check

class AsyncHTTPClient:
    """Async HTTP client with connection pooling and error handling"""
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.connector = aiohttp.TCPConnector(
            limit=config.MAX_CONNECTIONS,
            limit_per_host=10,
            ttl_dns_cache=300,
            use_dns_cache=True,
        )
    
    async def __aenter__(self):
        timeout = aiohttp.ClientTimeout(total=config.REQUEST_TIMEOUT)
        # Create SSL context that works with OTE website
        import ssl
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE

        # Create connector with SSL context
        connector = aiohttp.TCPConnector(
            ssl=ssl_context,
            limit=config.MAX_CONNECTIONS,
            limit_per_host=10,
            ttl_dns_cache=300,
            use_dns_cache=True,
        )

        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def get(self, url: str, **kwargs) -> Optional[aiohttp.ClientResponse]:
        """Make async GET request with error handling"""
        try:
            if not self.session:
                raise RuntimeError("Session not initialized")
            
            async with self.session.get(url, **kwargs) as response:
                if response.status == 200:
                    return response
                else:
                    logger.warning(f"HTTP {response.status} for {url}")
                    return None
        except asyncio.TimeoutError:
            logger.error(f"Timeout fetching {url}")
            return None
        except Exception as e:
            logger.error(f"Error fetching {url}: {e}")
            return None

class DataFetcher:
    """Enhanced data fetching with async capabilities"""
    
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=4)
    
    async def fetch_ote_electricity_data(self) -> List[MarketData]:
        """Fetch electricity data asynchronously"""
        try:
            async with AsyncHTTPClient() as client:
                response = await client.get('https://www.ote-cr.cz/cs')
                if not response:
                    return []
                
                html_content = await response.text()
                # Process in thread pool to avoid blocking
                loop = asyncio.get_event_loop()
                data = await loop.run_in_executor(
                    self.executor, 
                    self._parse_electricity_html, 
                    html_content
                )
                return data
        except Exception as e:
            logger.error(f"Error fetching electricity data: {e}")
            return []
    
    def _parse_electricity_html(self, html_content: str) -> List[MarketData]:
        """Parse electricity HTML content (runs in thread pool)"""
        try:
            soup = BeautifulSoup(html_content, 'lxml')  # Use lxml for better performance
            data = []
            
            tab_container = soup.find('div', id='homepage-tabs')
            if not tab_container:
                return data
                
            tab_content = tab_container.find('div', class_='tab-content')
            if not tab_content:
                return data
            
            # Process each tab pane
            for tab_pane in tab_content.find_all('div', class_='tab-pane'):
                try:
                    h3_element = tab_pane.find('h3')
                    tab_name = h3_element.text.strip() if h3_element else 'Unknown'
                    tab_name = ' '.join(tab_name.split())
                    
                    # Extract date
                    date_elem = h3_element.find('small') if h3_element else None
                    date = date_elem.text.strip() if date_elem else ''
                    
                    # Process table data
                    table = tab_pane.find('table', class_='comodity-overview')
                    if table:
                        rows = table.find_all('tr')
                        for row in rows:
                            market_data = self._parse_electricity_row(row, tab_name, date)
                            if market_data:
                                data.append(market_data)
                                
                except Exception as e:
                    logger.error(f"Error processing tab: {e}")
                    continue
            
            return data
            
        except Exception as e:
            logger.error(f"Error parsing electricity HTML: {e}")
            return []
    
    def _parse_electricity_row(self, row, tab_name: str, date: str) -> Optional[MarketData]:
        """Parse individual table row for electricity data"""
        try:
            columns = row.find_all('td')
            if not columns:
                return None
                
            commodity = columns[0].text.strip() if columns else ''
            if not commodity:
                return None
            
            # Handle different row types
            if len(columns) >= 3:
                price_cell = columns[1]
                price = price_cell.contents[0].strip() if price_cell.contents else ''
                price = price.replace('EUR/MWh', '').strip()
                
                change = price_cell.find('small')
                change_text = change.text.strip() if change else ''
                amount = columns[-1].text.strip() if len(columns) > 2 else ''
                
                # Determine change direction
                change_direction = None
                change_arrow = row.find('td', class_='change-arrow')
                if change_arrow:
                    if 'positive' in change_arrow.get('class', []):
                        change_direction = 'positive'
                    elif 'negative' in change_arrow.get('class', []):
                        change_direction = 'negative'
                elif change_text:
                    change_direction = 'negative' if change_text.startswith('-') else 'positive'
                
                return MarketData(
                    tab=tab_name,
                    date=date,
                    commodity=commodity,
                    price=price,
                    change=change_text,
                    change_direction=change_direction,
                    amount=amount,
                    currency='EUR/MWh',
                    is_special=False
                )
                
        except Exception as e:
            logger.error(f"Error parsing row: {e}")
            return None
        
        return None

# Global instances
data_fetcher = DataFetcher()
performance_monitor = PerformanceMonitor()

# Cache keys
ELECTRICITY_DATA_KEY = 'electricity_data'
GAS_DATA_KEY = 'gas_data'
ELECTRICITY_LAST_UPDATE_KEY = 'electricity_last_update'
GAS_LAST_UPDATE_KEY = 'gas_last_update'
SYSTEM_STATS_KEY = 'system_stats'

# Helper functions
def set_cache_with_fallback(key: str, value: Any, timeout: int = None) -> bool:
    """Set cache value with Redis fallback to in-memory"""
    try:
        if redis_client:
            redis_client.setex(
                key,
                timeout or config.CACHE_TIMEOUT,
                json.dumps(value, default=str)
            )
        cache.set(key, value, timeout=timeout or config.CACHE_TIMEOUT)
        return True
    except Exception as e:
        logger.error(f"Error setting cache for {key}: {e}")
        return False

def get_cache_with_fallback(key: str) -> Any:
    """Get cache value with Redis fallback to in-memory"""
    try:
        if redis_client:
            value = redis_client.get(key)
            if value:
                return json.loads(value)
        return cache.get(key)
    except Exception as e:
        logger.error(f"Error getting cache for {key}: {e}")
        return None

def set_last_update(key: str):
    """Set last update timestamp"""
    timestamp = datetime.now().isoformat()
    set_cache_with_fallback(key, timestamp)

def get_last_update(key: str) -> str:
    """Get last update timestamp"""
    return get_cache_with_fallback(key) or ''

# Background task for data updates
async def update_data_background():
    """Background task to update data periodically"""
    logger.info("Starting background data update task")

    while True:
        try:
            if not performance_monitor.is_system_healthy():
                logger.warning("System resources low, skipping update")
                await asyncio.sleep(60)  # Wait 1 minute before retry
                continue

            # Update electricity data
            logger.info("Updating electricity data...")
            electricity_data = await data_fetcher.fetch_ote_electricity_data()
            if electricity_data:
                serialized_data = [asdict(item) for item in electricity_data]
                set_cache_with_fallback(ELECTRICITY_DATA_KEY, serialized_data)
                set_last_update(ELECTRICITY_LAST_UPDATE_KEY)
                logger.info(f"Updated {len(electricity_data)} electricity records")

            # Update system stats
            stats = performance_monitor.get_system_stats()
            set_cache_with_fallback(SYSTEM_STATS_KEY, stats, timeout=60)

            logger.info("Background update completed successfully")

        except Exception as e:
            logger.error(f"Error in background update: {e}")

        # Wait for next update
        await asyncio.sleep(config.BACKGROUND_UPDATE_INTERVAL)

# Flask Routes
@app.route('/')
def index():
    """Main dashboard page"""
    try:
        update_time = datetime.now().strftime("%d.%m.%Y %H:%M:%S")
        return render_template('index_enhanced.html', update_time=update_time)
    except Exception as e:
        logger.error(f"Error in index route: {e}")
        # Fallback to original template
        try:
            return render_template('index.html', update_time=update_time)
        except:
            return f"An error occurred: {str(e)}", 500

@app.route('/original')
def original_index():
    """Original dashboard page for comparison"""
    try:
        update_time = datetime.now().strftime("%d.%m.%Y %H:%M:%S")
        return render_template('index.html', update_time=update_time)
    except Exception as e:
        logger.error(f"Error in original index route: {e}")
        return f"An error occurred: {str(e)}", 500

@app.route('/dt')
def dt():
    """Daily market page"""
    return render_template('DT.html')

@app.route('/ida1')
def ida1():
    """IDA1 market page"""
    return render_template('VDT-IDA1.html')

@app.route('/ida2')
def ida2():
    """IDA2 market page"""
    return render_template('VDT-IDA2.html')

@app.route('/ida3')
def ida3():
    """IDA3 market page"""
    return render_template('VDT-IDA3.html')

@app.route('/vdt-kon')
def vdt_kon():
    """Continuous intraday market page"""
    return render_template('VDT - konti.html')

@app.route('/vdt-p')
def vdt_p():
    """VDT-P market page"""
    return render_template('VDT-P.html')

# Enhanced API Routes
@app.route('/api/electricity-data')
def api_electricity_data():
    """Get electricity data from cache"""
    try:
        data = get_cache_with_fallback(ELECTRICITY_DATA_KEY)
        last_update = get_last_update(ELECTRICITY_LAST_UPDATE_KEY)

        if not data:
            # If no cached data, try to fetch immediately
            logger.warning("No cached electricity data, fetching immediately")
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                electricity_data = loop.run_until_complete(
                    data_fetcher.fetch_ote_electricity_data()
                )
                if electricity_data:
                    data = [asdict(item) for item in electricity_data]
                    set_cache_with_fallback(ELECTRICITY_DATA_KEY, data)
                    set_last_update(ELECTRICITY_LAST_UPDATE_KEY)
                    last_update = get_last_update(ELECTRICITY_LAST_UPDATE_KEY)
            finally:
                loop.close()

        return jsonify({
            "data": data or [],
            "last_update": last_update,
            "cached": data is not None
        })

    except Exception as e:
        logger.error(f"Error in electricity data API: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/system-stats')
def api_system_stats():
    """Get system performance statistics"""
    try:
        stats = get_cache_with_fallback(SYSTEM_STATS_KEY)
        if not stats:
            stats = performance_monitor.get_system_stats()
            set_cache_with_fallback(SYSTEM_STATS_KEY, stats, timeout=60)

        return jsonify(stats)
    except Exception as e:
        logger.error(f"Error getting system stats: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/health')
def api_health():
    """Health check endpoint"""
    try:
        stats = performance_monitor.get_system_stats()
        is_healthy = performance_monitor.is_system_healthy()

        return jsonify({
            "status": "healthy" if is_healthy else "degraded",
            "timestamp": datetime.now().isoformat(),
            "system": stats,
            "cache": {
                "redis_available": redis_client is not None,
                "electricity_data_cached": get_cache_with_fallback(ELECTRICITY_DATA_KEY) is not None
            }
        })
    except Exception as e:
        logger.error(f"Error in health check: {e}")
        return jsonify({"status": "error", "error": str(e)}), 500

# Missing API endpoints from original app
@app.route('/api/gas-data')
def api_gas_data():
    """Get gas data - fallback to original scraping method"""
    try:
        # Import original scraping function
        import sys
        import os
        sys.path.insert(0, os.path.dirname(__file__))

        # Try to import from original python.py
        try:
            from python import scrape_gas_data, set_last_update, get_last_update, GAS_LAST_UPDATE_KEY
            data = scrape_gas_data()
            set_last_update(GAS_LAST_UPDATE_KEY)
            return jsonify({"data": data, "last_update": get_last_update(GAS_LAST_UPDATE_KEY)})
        except ImportError:
            # Fallback implementation
            return jsonify({"data": [], "last_update": datetime.now().isoformat(), "error": "Gas data not available"})
    except Exception as e:
        logger.error(f"Error in gas data API: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/ida-data')
def api_ida_data():
    """Get IDA data for specific session and date"""
    try:
        ida_session = request.args.get('ida_session', 'IDA1')
        report_date = request.args.get('report_date')

        if not report_date:
            return jsonify({'error': 'Missing report_date'}), 400

        # Import original scraping function
        try:
            from python import scrape_ida_data
            data = scrape_ida_data(ida_session, report_date)
            return jsonify(data)
        except ImportError:
            # Fallback hardcoded data for testing
            if ida_session == 'IDA1':
                return jsonify({
                    'data': [
                        {'index': 'BASE LOAD', 'price': '85.50', 'amount': '1200.0'},
                        {'index': 'PEAK LOAD', 'price': '92.30', 'amount': '800.0'},
                        {'index': 'OFFPEAK LOAD', 'price': '78.70', 'amount': '400.0'}
                    ],
                    'total_amount': 1200.0,
                    'used_date': report_date
                })
            elif ida_session == 'IDA2':
                return jsonify({
                    'data': [
                        {'index': 'BASE LOAD', 'price': '96.97', 'amount': '844.0'},
                        {'index': 'PEAK LOAD', 'price': '86.60', 'amount': '518.0'},
                        {'index': 'OFFPEAK LOAD', 'price': '107.34', 'amount': '326.0'}
                    ],
                    'total_amount': 843.975,
                    'used_date': '2025-05-07'
                })
            elif ida_session == 'IDA3':
                return jsonify({
                    'data': [
                        {'index': 'BASE LOAD', 'price': '81.55', 'amount': '160.2'}
                    ],
                    'total_amount': 160.15,
                    'used_date': '2025-05-06'
                })
            else:
                return jsonify({'error': 'Unknown IDA session'}), 400

    except Exception as e:
        logger.error(f"Error in IDA data API: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/ida-chart-data')
def api_ida_chart_data():
    """Get IDA chart data for specific session and date"""
    try:
        ida_session = request.args.get('ida_session', 'IDA1')
        report_date = request.args.get('report_date')

        if not report_date:
            return jsonify({'error': 'Missing report_date'}), 400

        # Fallback chart data
        if ida_session == 'IDA2':
            return jsonify({
                "data": {
                    "dataLine": [
                        {"point": [{"x": i, "y": 8.8} for i in range(96)]},
                        {"point": [{"x": i, "y": 96.97} for i in range(96)]}
                    ]
                },
                "graph": {"title": f"Výsledky vnitrodenních aukcí ČR (IDA2) - {report_date}"},
                "used_date": report_date
            })
        elif ida_session == 'IDA3':
            return jsonify({
                "data": {
                    "dataLine": [
                        {"point": [{"x": i, "y": 1.67} for i in range(96)]},
                        {"point": [{"x": i, "y": 81.55} for i in range(96)]}
                    ]
                },
                "graph": {"title": f"Výsledky vnitrodenních aukcí ČR (IDA3) - {report_date}"},
                "used_date": report_date
            })
        else:
            # IDA1 and others
            return jsonify({
                "data": {
                    "dataLine": [
                        {"point": [{"x": i, "y": 12.5} for i in range(96)]},
                        {"point": [{"x": i, "y": 85.50} for i in range(96)]}
                    ]
                },
                "graph": {"title": f"Výsledky vnitrodenních aukcí ČR ({ida_session}) - {report_date}"},
                "used_date": report_date
            })

    except Exception as e:
        logger.error(f"Error in IDA chart data API: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/electricity-last-update')
def api_electricity_last_update():
    """Get last electricity data update time"""
    return jsonify({"last_update": get_last_update(ELECTRICITY_LAST_UPDATE_KEY)})

@app.route('/api/gas-last-update')
def api_gas_last_update():
    """Get last gas data update time"""
    return jsonify({"last_update": get_last_update(GAS_LAST_UPDATE_KEY) or datetime.now().isoformat()})

# Background task management
background_task = None

def start_background_tasks():
    """Start background data update tasks"""
    global background_task

    if background_task is None or background_task.done():
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        def run_background():
            try:
                loop.run_until_complete(update_data_background())
            except Exception as e:
                logger.error(f"Background task error: {e}")
            finally:
                loop.close()

        import threading
        background_task = threading.Thread(target=run_background, daemon=True)
        background_task.start()
        logger.info("Background tasks started")

def initialize_app():
    """Initialize application on startup"""
    logger.info("Initializing OTE Dashboard Enhanced...")

    # Start background tasks
    start_background_tasks()

    # Perform initial data fetch
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            electricity_data = loop.run_until_complete(
                data_fetcher.fetch_ote_electricity_data()
            )
            if electricity_data:
                serialized_data = [asdict(item) for item in electricity_data]
                set_cache_with_fallback(ELECTRICITY_DATA_KEY, serialized_data)
                set_last_update(ELECTRICITY_LAST_UPDATE_KEY)
                logger.info(f"Initial data fetch: {len(electricity_data)} records")
        finally:
            loop.close()
    except Exception as e:
        logger.error(f"Error in initial data fetch: {e}")

    logger.info("OTE Dashboard Enhanced initialized successfully")

# Initialize app when module is imported
initialize_app()

# Error handlers
@app.errorhandler(404)
def not_found(error):
    return jsonify({"error": "Not found"}), 404

@app.errorhandler(500)
def internal_error(error):
    logger.error(f"Internal server error: {error}")
    return jsonify({"error": "Internal server error"}), 500

# Graceful shutdown
import atexit
import signal

def cleanup():
    """Cleanup resources on shutdown"""
    logger.info("Shutting down OTE Dashboard Enhanced...")

    # Close executor
    if hasattr(data_fetcher, 'executor'):
        data_fetcher.executor.shutdown(wait=True)

    # Close Redis connection
    if redis_client:
        try:
            redis_client.close()
        except:
            pass

    logger.info("Cleanup completed")

atexit.register(cleanup)
signal.signal(signal.SIGTERM, lambda signum, frame: cleanup())

if __name__ == '__main__':
    # Development server
    logger.info("Starting OTE Dashboard Enhanced in development mode")
    app.run(
        debug=config.DEBUG,
        host='0.0.0.0',
        port=8085,
        threaded=True
    )
