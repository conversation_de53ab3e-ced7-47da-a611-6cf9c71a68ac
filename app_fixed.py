"""
OTE Dashboard - Fixed Version
Combines working original functionality with performance enhancements
"""

import os
import logging
from datetime import datetime
from flask import Flask, render_template, jsonify, request
from flask_cors import CORS
from flask_caching import Cache

# Import all working functions from original app
from python import (
    scrape_ote_data, scrape_gas_data, scrape_ida_data,
    set_last_update, get_last_update,
    ELECTRICITY_LAST_UPDATE_KEY, GAS_LAST_UPDATE_KEY
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Flask app setup
app = Flask(__name__, 
           static_folder='static',
           template_folder='templates')
CORS(app)

# Simple caching configuration
cache_config = {
    'CACHE_TYPE': 'SimpleCache',
    'CACHE_DEFAULT_TIMEOUT': 900  # 15 minutes
}
cache = Cache(app, config=cache_config)

# Routes for all pages
@app.route('/')
def index():
    """Main dashboard page - enhanced version"""
    try:
        update_time = datetime.now().strftime("%d.%m.%Y %H:%M:%S")
        return render_template('index_enhanced.html', update_time=update_time)
    except Exception as e:
        logger.error(f"Error in enhanced index route: {e}")
        # Fallback to original
        try:
            return render_template('index.html', update_time=update_time)
        except:
            return f"An error occurred: {str(e)}", 500

@app.route('/original')
def original_index():
    """Original dashboard page for comparison"""
    try:
        update_time = datetime.now().strftime("%d.%m.%Y %H:%M:%S")
        return render_template('index.html', update_time=update_time)
    except Exception as e:
        logger.error(f"Error in original index route: {e}")
        return f"An error occurred: {str(e)}", 500

@app.route('/dt')
def dt():
    """Daily market page"""
    return render_template('DT.html')

@app.route('/ida1')
def ida1():
    """IDA1 market page"""
    return render_template('VDT-IDA1.html')

@app.route('/ida2')
def ida2():
    """IDA2 market page"""
    return render_template('VDT-IDA2.html')

@app.route('/ida3')
def ida3():
    """IDA3 market page"""
    return render_template('VDT-IDA3.html')

@app.route('/vdt-kon')
def vdt_kon():
    """Continuous intraday market page"""
    return render_template('VDT - konti.html')

@app.route('/vdt-p')
def vdt_p():
    """VDT-P market page"""
    return render_template('VDT-P.html')

# Additional routes for compatibility
@app.route('/vdt-ida1')
def vdt_ida1():
    return render_template('VDT-IDA1.html')

@app.route('/vdt-ida2')
def vdt_ida2():
    return render_template('VDT-IDA2.html')

@app.route('/vdt-ida3')
def vdt_ida3():
    return render_template('VDT-IDA3.html')

# API Routes - using original working functions
@app.route('/api/electricity-data')
@cache.cached()
def electricity_data():
    """Get electricity data using original working function"""
    try:
        data = scrape_ote_data()
        set_last_update(ELECTRICITY_LAST_UPDATE_KEY)
        return jsonify({
            "data": data, 
            "last_update": get_last_update(ELECTRICITY_LAST_UPDATE_KEY),
            "cached": True
        })
    except Exception as e:
        logger.error(f"Error in electricity data API: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/gas-data')
@cache.cached()
def gas_data():
    """Get gas data using original working function"""
    try:
        data = scrape_gas_data()
        set_last_update(GAS_LAST_UPDATE_KEY)
        return jsonify({
            "data": data, 
            "last_update": get_last_update(GAS_LAST_UPDATE_KEY),
            "cached": True
        })
    except Exception as e:
        logger.error(f"Error in gas data API: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/ida-data')
def api_ida_data():
    """Get IDA data using original working function"""
    try:
        ida_session = request.args.get('ida_session', 'IDA1')
        report_date = request.args.get('report_date')
        
        if not report_date:
            return jsonify({'error': 'Missing report_date'}), 400
        
        data = scrape_ida_data(ida_session, report_date)
        return jsonify(data)
    except Exception as e:
        logger.error(f"Error in IDA data API: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/ida-chart-data')
def api_ida_chart_data():
    """Get IDA chart data - using original logic"""
    try:
        ida_session = request.args.get('ida_session', 'IDA1')
        report_date = request.args.get('report_date')
        
        if not report_date:
            return jsonify({'error': 'Missing report_date'}), 400
        
        # Import original chart data functions
        from python import api_ida2_chart_data, api_ida3_chart_data, api_ida_generic_chart_data
        
        if ida_session == 'IDA2':
            return api_ida2_chart_data(report_date)
        elif ida_session == 'IDA3':
            return api_ida3_chart_data(report_date)
        else:
            return api_ida_generic_chart_data(ida_session, report_date)
            
    except Exception as e:
        logger.error(f"Error in IDA chart data API: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/electricity-last-update')
def electricity_last_update():
    """Get last electricity data update time"""
    return jsonify({"last_update": get_last_update(ELECTRICITY_LAST_UPDATE_KEY)})

@app.route('/api/gas-last-update')
def gas_last_update():
    """Get last gas data update time"""
    return jsonify({"last_update": get_last_update(GAS_LAST_UPDATE_KEY)})

@app.route('/api/ida1-data')
@cache.cached()
def ida1_data():
    """IDA1 specific data endpoint"""
    try:
        from python import ida1_data as original_ida1_data
        return original_ida1_data()
    except Exception as e:
        logger.error(f"Error in IDA1 data API: {e}")
        return jsonify({"error": str(e)}), 500

# Health check endpoint
@app.route('/api/health')
def api_health():
    """Simple health check"""
    try:
        return jsonify({
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "app": "OTE Dashboard Fixed",
            "version": "1.0"
        })
    except Exception as e:
        logger.error(f"Error in health check: {e}")
        return jsonify({"status": "error", "error": str(e)}), 500

# Error handlers
@app.errorhandler(404)
def not_found(error):
    return jsonify({"error": "Not found"}), 404

@app.errorhandler(500)
def internal_error(error):
    logger.error(f"Internal server error: {error}")
    return jsonify({"error": "Internal server error"}), 500

if __name__ == '__main__':
    logger.info("Starting OTE Dashboard Fixed Version...")
    app.run(
        debug=False,
        host='0.0.0.0',
        port=8085,  # Standard port
        threaded=True
    )
