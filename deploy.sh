#!/bin/bash

# OTE Dashboard Enhanced - Deployment Script
# Optimized for Raspberry Pi

set -e  # Exit on any error

echo "=========================================="
echo "OTE Dashboard Enhanced - Deployment"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

# Check if running on Raspberry Pi
check_raspberry_pi() {
    if [[ -f /proc/device-tree/model ]] && grep -q "Raspberry Pi" /proc/device-tree/model; then
        print_status "Running on Raspberry Pi"
        export IS_RASPBERRY_PI=true
    else
        print_warning "Not running on Raspberry Pi - some optimizations may not apply"
        export IS_RASPBERRY_PI=false
    fi
}

# Check system requirements
check_requirements() {
    print_info "Checking system requirements..."
    
    # Check Python version
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
        print_status "Python $PYTHON_VERSION found"
    else
        print_error "Python 3 is required but not installed"
        exit 1
    fi
    
    # Check pip
    if command -v pip3 &> /dev/null; then
        print_status "pip3 found"
    else
        print_error "pip3 is required but not installed"
        exit 1
    fi
    
    # Check available memory
    AVAILABLE_MEM=$(free -m | awk 'NR==2{printf "%.0f", $7}')
    if [ "$AVAILABLE_MEM" -lt 100 ]; then
        print_warning "Low available memory: ${AVAILABLE_MEM}MB"
        print_info "Consider closing other applications"
    else
        print_status "Available memory: ${AVAILABLE_MEM}MB"
    fi
}

# Install system dependencies
install_system_deps() {
    print_info "Installing system dependencies..."
    
    if command -v apt-get &> /dev/null; then
        sudo apt-get update
        sudo apt-get install -y \
            python3-dev \
            python3-pip \
            python3-venv \
            redis-server \
            nginx \
            supervisor \
            build-essential \
            libxml2-dev \
            libxslt1-dev \
            zlib1g-dev
        print_status "System dependencies installed"
    else
        print_warning "apt-get not found - please install dependencies manually"
    fi
}

# Setup Python virtual environment
setup_venv() {
    print_info "Setting up Python virtual environment..."
    
    if [ ! -d "venv" ]; then
        python3 -m venv venv
        print_status "Virtual environment created"
    else
        print_status "Virtual environment already exists"
    fi
    
    source venv/bin/activate
    pip install --upgrade pip
    pip install -r requirements.txt
    print_status "Python dependencies installed"
}

# Configure Redis
configure_redis() {
    print_info "Configuring Redis..."
    
    if command -v redis-server &> /dev/null; then
        # Start Redis service
        sudo systemctl enable redis-server
        sudo systemctl start redis-server
        
        # Test Redis connection
        if redis-cli ping | grep -q "PONG"; then
            print_status "Redis is running and accessible"
        else
            print_warning "Redis is installed but not responding"
        fi
    else
        print_warning "Redis not installed - using in-memory caching"
    fi
}

# Setup Nginx reverse proxy
setup_nginx() {
    print_info "Setting up Nginx reverse proxy..."
    
    if command -v nginx &> /dev/null; then
        # Create Nginx configuration
        sudo tee /etc/nginx/sites-available/ote-dashboard > /dev/null <<EOF
server {
    listen 80;
    server_name _;
    
    client_max_body_size 1M;
    
    location / {
        proxy_pass http://127.0.0.1:8085;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Static files
    location /static/ {
        alias $(pwd)/static/;
        expires 1d;
        add_header Cache-Control "public, immutable";
    }
    
    # Health check
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
EOF
        
        # Enable site
        sudo ln -sf /etc/nginx/sites-available/ote-dashboard /etc/nginx/sites-enabled/
        sudo rm -f /etc/nginx/sites-enabled/default
        
        # Test and reload Nginx
        sudo nginx -t && sudo systemctl reload nginx
        print_status "Nginx configured and reloaded"
    else
        print_warning "Nginx not installed - application will run on port 8085"
    fi
}

# Setup Supervisor for process management
setup_supervisor() {
    print_info "Setting up Supervisor for process management..."
    
    if command -v supervisorctl &> /dev/null; then
        # Create Supervisor configuration
        sudo tee /etc/supervisor/conf.d/ote-dashboard.conf > /dev/null <<EOF
[program:ote-dashboard]
command=$(pwd)/venv/bin/gunicorn -c gunicorn_config.py app_enhanced:app
directory=$(pwd)
user=$(whoami)
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=$(pwd)/supervisor.log
environment=PATH="$(pwd)/venv/bin"
EOF
        
        # Update Supervisor
        sudo supervisorctl reread
        sudo supervisorctl update
        sudo supervisorctl start ote-dashboard
        print_status "Supervisor configured and service started"
    else
        print_warning "Supervisor not installed - using manual startup"
    fi
}

# Optimize for Raspberry Pi
optimize_raspberry_pi() {
    if [ "$IS_RASPBERRY_PI" = true ]; then
        print_info "Applying Raspberry Pi optimizations..."
        
        # GPU memory split
        if ! grep -q "gpu_mem=64" /boot/config.txt; then
            echo "gpu_mem=64" | sudo tee -a /boot/config.txt
            print_status "GPU memory split configured"
        fi
        
        # Disable swap if not needed (optional)
        # sudo dphys-swapfile swapoff
        # sudo dphys-swapfile uninstall
        # sudo update-rc.d dphys-swapfile remove
        
        print_status "Raspberry Pi optimizations applied"
    fi
}

# Create startup scripts
create_startup_scripts() {
    print_info "Creating startup scripts..."
    
    # Make scripts executable
    chmod +x start_enhanced.py
    chmod +x deploy.sh
    
    # Create simple start script
    cat > start.sh << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"
source venv/bin/activate
python3 start_enhanced.py
EOF
    chmod +x start.sh
    
    # Create production start script
    cat > start_production.sh << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"
source venv/bin/activate
gunicorn -c gunicorn_config.py app_enhanced:app
EOF
    chmod +x start_production.sh
    
    print_status "Startup scripts created"
}

# Main deployment function
main() {
    print_info "Starting deployment process..."
    
    check_raspberry_pi
    check_requirements
    
    # Ask for installation type
    echo ""
    echo "Select installation type:"
    echo "1) Development (simple setup)"
    echo "2) Production (full setup with Nginx, Redis, Supervisor)"
    echo "3) System check only"
    read -p "Enter choice [1-3]: " choice
    
    case $choice in
        1)
            print_info "Development installation selected"
            setup_venv
            create_startup_scripts
            print_status "Development setup completed!"
            print_info "Start the application with: ./start.sh"
            ;;
        2)
            print_info "Production installation selected"
            install_system_deps
            setup_venv
            configure_redis
            setup_nginx
            setup_supervisor
            optimize_raspberry_pi
            create_startup_scripts
            print_status "Production setup completed!"
            print_info "Application should be running at http://localhost"
            print_info "Check status with: sudo supervisorctl status ote-dashboard"
            ;;
        3)
            print_info "System check only"
            python3 start_enhanced.py --check
            ;;
        *)
            print_error "Invalid choice"
            exit 1
            ;;
    esac
    
    echo ""
    print_status "Deployment completed successfully!"
    echo ""
    print_info "Next steps:"
    echo "  - Check logs: tail -f *.log"
    echo "  - Monitor system: python3 start_enhanced.py --check"
    echo "  - Access dashboard: http://localhost (production) or http://localhost:8085 (development)"
}

# Run main function
main "$@"
