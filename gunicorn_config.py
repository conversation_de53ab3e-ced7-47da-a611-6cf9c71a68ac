"""
Gunicorn configuration for OTE Dashboard Enhanced
Optimized for Raspberry Pi deployment
"""

import os
import multiprocessing
from pathlib import Path

# Server socket
bind = f"0.0.0.0:{os.getenv('PORT', '8085')}"
backlog = 2048

# Worker processes
workers = int(os.getenv('WORKERS', min(2, multiprocessing.cpu_count())))
worker_class = "sync"
worker_connections = 1000
timeout = 30
keepalive = 2

# Restart workers after this many requests, to help prevent memory leaks
max_requests = 1000
max_requests_jitter = 50

# Logging
accesslog = "access.log"
errorlog = "error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# Process naming
proc_name = 'ote-dashboard-enhanced'

# Server mechanics
daemon = False
pidfile = 'ote-dashboard.pid'
user = None
group = None
tmp_upload_dir = None

# SSL (if needed)
# keyfile = None
# certfile = None

# Performance tuning for Raspberry Pi
preload_app = True
enable_stdio_inheritance = True

# Memory optimization
worker_tmp_dir = '/dev/shm'  # Use RAM for temporary files

def when_ready(server):
    """Called just after the server is started."""
    server.log.info("OTE Dashboard Enhanced is ready to serve requests")

def worker_int(worker):
    """Called just after a worker exited on SIGINT or SIGQUIT."""
    worker.log.info("Worker received INT or QUIT signal")

def pre_fork(server, worker):
    """Called just before a worker is forked."""
    server.log.info("Worker spawned (pid: %s)", worker.pid)

def post_fork(server, worker):
    """Called just after a worker has been forked."""
    server.log.info("Worker spawned (pid: %s)", worker.pid)

def post_worker_init(worker):
    """Called just after a worker has initialized the application."""
    worker.log.info("Worker initialized (pid: %s)", worker.pid)

def worker_abort(worker):
    """Called when a worker received the SIGABRT signal."""
    worker.log.info("Worker received SIGABRT signal")
