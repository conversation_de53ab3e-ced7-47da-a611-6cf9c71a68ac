<header class="enhanced-header">
  <div class="header-content">
    <div class="header-logo">
      <img src="{{ url_for('static', filename='OTE.png') }}" alt="Logo OTE" class="h-16 hover:scale-105 transition-transform duration-300">
      <div>
        <div class="header-title">Operátor trhu s energiemi</div>
        <div style="opacity: 0.9; font-size: 1rem; margin: 0;"><PERSON><PERSON><PERSON><PERSON><PERSON> trhy a <PERSON>ří<PERSON>ž<PERSON>i</div>
      </div>
    </div>
    <div class="header-time" id="dateTime">{{ current_time or 'Načítání...' }}</div>
  </div>
</header>

<!-- Enhanced Progress Bar -->
<div class="progress-bar-container" id="autoSwitchProgressBarWrapper">
  <div class="progress-bar" id="autoSwitchProgressBar" style="width: 100%"></div>
</div>

<style>
  /* Enhanced Header Styles */
  .enhanced-header {
    background: linear-gradient(135deg, var(--primary-blue-dark) 0%, var(--primary-blue) 50%, var(--primary-blue-light) 100%);
    color: white;
    padding: var(--space-lg) 0;
    box-shadow: 0 4px 20px rgba(30, 64, 175, 0.3);
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
  }

  .header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-xl);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .header-logo {
    display: flex;
    align-items: center;
    gap: var(--space-md);
  }

  .header-logo img {
    height: 3rem;
    transition: transform var(--transition-normal);
  }

  .header-logo img:hover {
    transform: scale(1.05);
  }

  .header-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
  }

  .header-time {
    font-size: 1.25rem;
    font-weight: 600;
    background: rgba(255, 255, 255, 0.2);
    padding: var(--space-sm) var(--space-lg);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    font-variant-numeric: tabular-nums;
  }

  .progress-bar-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: rgba(0, 0, 0, 0.1);
    z-index: 1001;
  }

  .progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-blue), var(--accent-amber));
    transition: width 1s linear;
    box-shadow: 0 0 10px rgba(30, 64, 175, 0.5);
  }

  /* Status Indicator Styles */
  .status-indicator {
    position: fixed;
    top: 6rem;
    right: 2rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 1rem;
    padding: 1rem 1.5rem;
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--neutral-gray);
    z-index: 100;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .status-dot {
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    background: var(--secondary-green);
    animation: pulse 2s ease-in-out infinite;
  }

  .loading-spinner {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    border: 3px solid rgba(59, 130, 246, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-blue);
    animation: spin 1s ease-in-out infinite;
  }

  @keyframes spin {
    to { transform: rotate(360deg); }
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.2); }
  }
</style>
