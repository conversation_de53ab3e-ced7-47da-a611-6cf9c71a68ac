<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON><PERSON> vni<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (IDA1) - Enhanced</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enhanced-styles.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.0/chart.min.js" defer></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            fontFamily: {
              sans: ['Inter', 'sans-serif'],
            },
            animation: {
              'gradient-x': 'gradient-x 15s ease infinite',
            },
            keyframes: {
              'gradient-x': {
                '0%, 100%': {
                  'background-size': '200% 200%',
                  'background-position': 'left center'
                },
                '50%': {
                  'background-size': '200% 200%',
                  'background-position': 'right center'
                }
              },
            }
          }
        }
      }
    </script>

    <style>
        /* Enhanced IDA1 Specific Styles */
        .ida-header {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
            color: white;
            padding: 2.5rem 0;
            position: relative;
            overflow: hidden;
        }

        .ida-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            animation: float 15s ease-in-out infinite;
        }

        .ida-title {
            font-size: 2.8rem;
            font-weight: 900;
            text-align: center;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 1;
        }

        .ida-subtitle {
            color: #bfdbfe;
            font-weight: 600;
            font-size: 1.2rem;
            margin-top: 0.5rem;
        }

        .session-badge {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            padding: 0.5rem 1.5rem;
            border-radius: 2rem;
            font-weight: 700;
            font-size: 1rem;
            margin-top: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        .ida-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .ida-metric-card {
            background: var(--glass-bg);
            backdrop-filter: blur(var(--glass-blur));
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-xl);
            padding: 2.5rem;
            text-align: center;
            transition: all var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .ida-metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
        }

        .ida-metric-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 25px 50px rgba(59, 130, 246, 0.2);
        }

        .ida-metric-icon {
            width: 4rem;
            height: 4rem;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border-radius: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 8px 16px rgba(59, 130, 246, 0.3);
        }

        .ida-metric-label {
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--neutral-gray);
            text-transform: uppercase;
            letter-spacing: 0.1em;
            margin-bottom: 1rem;
        }

        .ida-metric-value {
            font-size: 3.2rem;
            font-weight: 900;
            color: var(--primary-blue);
            margin: 1rem 0;
            font-variant-numeric: tabular-nums;
        }

        .ida-metric-unit {
            font-size: 1rem;
            color: var(--neutral-gray);
            font-weight: 600;
        }

        .ida-chart-container {
            background: var(--glass-bg);
            backdrop-filter: blur(var(--glass-blur));
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-2xl);
            padding: 2.5rem;
            box-shadow: var(--glass-shadow);
        }

        .chart-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .chart-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--neutral-dark);
            margin-bottom: 0.5rem;
        }

        .chart-subtitle {
            color: var(--neutral-gray);
            font-size: 1rem;
        }
    </style>
</head>
<body>
    {% include 'partials/header.html' %}

    <!-- Status Indicator -->
    <div class="status-indicator" id="statusIndicator">
        <div class="status-dot"></div>
        <span>Načítání IDA1 dat...</span>
    </div>

    <!-- IDA Header -->
    <section class="ida-header">
        <div class="max-w-7xl mx-auto px-8 text-center">
            <h1 class="ida-title">
                Vnitrodenní aukce ČR
                <div class="ida-subtitle" id="market-date">Načítání...</div>
            </h1>
            <div class="session-badge">
                <i class="fas fa-bolt mr-2"></i>
                IDA1 Session
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-8 py-12">
        <!-- IDA Metrics Grid -->
        <div class="ida-metrics animate-stagger">
            <div class="ida-metric-card">
                <div class="ida-metric-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="ida-metric-label">Base Load</div>
                <div class="ida-metric-value" id="base-load">
                    <div class="loading-spinner"></div>
                </div>
                <div class="ida-metric-unit">EUR/MWh</div>
            </div>

            <div class="ida-metric-card">
                <div class="ida-metric-icon">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <div class="ida-metric-label">Peak Load</div>
                <div class="ida-metric-value" id="peak-load">
                    <div class="loading-spinner"></div>
                </div>
                <div class="ida-metric-unit">EUR/MWh</div>
            </div>

            <div class="ida-metric-card">
                <div class="ida-metric-icon">
                    <i class="fas fa-chart-area"></i>
                </div>
                <div class="ida-metric-label">Offpeak Load</div>
                <div class="ida-metric-value" id="offpeak-load">
                    <div class="loading-spinner"></div>
                </div>
                <div class="ida-metric-unit">EUR/MWh</div>
            </div>

            <div class="ida-metric-card">
                <div class="ida-metric-icon">
                    <i class="fas fa-bolt"></i>
                </div>
                <div class="ida-metric-label">Celkové množství</div>
                <div class="ida-metric-value" id="total-load">
                    <div class="loading-spinner"></div>
                </div>
                <div class="ida-metric-unit">MWh</div>
            </div>
        </div>

        <!-- Chart Container -->
        <div class="ida-chart-container animate-fade-in-up">
            <div class="chart-header">
                <h2 class="chart-title">Průběh obchodování IDA1</h2>
                <p class="chart-subtitle">Množství a ceny v průběhu dne</p>
            </div>
            <div class="relative w-full h-[650px]">
                <canvas id="oteChart"></canvas>
            </div>
        </div>
    </main>

    <script>
        // Enhanced IDA1 JavaScript
        function updateDateTime() {
            const now = new Date();
            const options = {
                day: '2-digit', month: '2-digit', year: 'numeric',
                hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false
            };
            const dateTimeElement = document.getElementById('dateTime');
            if (dateTimeElement) {
                dateTimeElement.textContent = now.toLocaleString('cs-CZ', options);
            }
        }

        function updateStatus(message, isError = false) {
            const statusIndicator = document.getElementById('statusIndicator');
            const statusDot = statusIndicator.querySelector('.status-dot');
            const statusText = statusIndicator.querySelector('span');

            statusText.textContent = message;
            statusDot.style.background = isError ? '#dc2626' : '#059669';
        }

        function updateMetric(elementId, value, animate = true) {
            const element = document.getElementById(elementId);
            const spinner = element.querySelector('.loading-spinner');

            if (spinner) {
                spinner.remove();
            }

            if (animate && value !== '---') {
                const numValue = parseFloat(value);
                if (!isNaN(numValue)) {
                    let current = 0;
                    const increment = numValue / 30;
                    const timer = setInterval(() => {
                        current += increment;
                        if (current >= numValue) {
                            element.textContent = numValue.toFixed(2);
                            clearInterval(timer);
                        } else {
                            element.textContent = current.toFixed(2);
                        }
                    }, 50);
                } else {
                    element.textContent = value;
                }
            } else {
                element.textContent = value;
            }
        }

        let timeUpdateInterval;
        document.addEventListener('DOMContentLoaded', () => {
            updateDateTime();
            timeUpdateInterval = setInterval(updateDateTime, 1000);
            document.addEventListener('visibilitychange', () => {
                if (document.hidden) {
                    clearInterval(timeUpdateInterval);
                } else {
                    updateDateTime();
                    timeUpdateInterval = setInterval(updateDateTime, 1000);
                }
            });
        });
    </script>
    <script src="{{ url_for('static', filename='js/IDA1.js') }}" defer></script>
    <script src="{{ url_for('static', filename='js/dashboard.js') }}" defer></script>
    <script>
      // Function to get cached data from localStorage
      function getCachedData(key) {
        const cached = localStorage.getItem(key);
        if (cached) {
          return JSON.parse(cached);
        }
        return null;
      }

      // Function to set data in localStorage
      function setCachedData(key, data) {
        localStorage.setItem(key, JSON.stringify(data));
      }

      async function fetchIdaSummary() {
        const date = getTodayDate();
        const cacheKey = `ida-summary-${date}`;
        const cachedData = getCachedData(cacheKey);
        if (cachedData) {
          return cachedData;
        }

        const url = `/api/ida-data?ida_session=IDA1&report_date=${date}`;
        try {
          const response = await fetch(url);
          if (!response.ok) throw new Error('Chyba při načítání dat z backendu');
          const data = await response.json();
          setCachedData(cacheKey, data);
          return data;
        } catch (e) {
          return null;
        }
      }

      async function updateSummaryCards() {
        const summary = await fetchIdaSummary();
        if (summary && summary.data) {
          for (const item of summary.data) {
            if (item.index === 'BASE LOAD') {
              document.getElementById('base-load').textContent = item.price || '---';
            } else if (item.index === 'PEAK LOAD') {
              document.getElementById('peak-load').textContent = item.price || '---';
            } else if (item.index === 'OFFPEAK LOAD') {
              document.getElementById('offpeak-load').textContent = item.price || '---';
            }
          }
          document.getElementById('total-load').textContent = summary.total_amount !== null ? summary.total_amount.toFixed(1) : '---';
        } else {
          document.getElementById('base-load').textContent = '---';
          document.getElementById('peak-load').textContent = '---';
          document.getElementById('offpeak-load').textContent = '---';
          document.getElementById('total-load').textContent = '---';
        }
      }

      window.onload = function() {
        updateDashboard();
        updateSummaryCards();
        updateDateTime();
        setInterval(updateDashboard, 5 * 60 * 1000);
        setInterval(updateSummaryCards, 5 * 60 * 1000);
      };
    </script>
</body>
</html>
