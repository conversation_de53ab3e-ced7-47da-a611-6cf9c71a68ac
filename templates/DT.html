<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>V<PERSON><PERSON><PERSON><PERSON> denn<PERSON><PERSON> trhu <PERSON>R - Enhanced</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enhanced-styles.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.0/chart.min.js" defer></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            fontFamily: {
              sans: ['Inter', 'sans-serif'],
            },
            colors: {
              'ote-blue': '#004C97',
              'ote-lightblue': '#E6F0F7',
            },
            animation: {
              'gradient-x': 'gradient-x 15s ease infinite',
            },
            keyframes: {
              'gradient-x': {
                '0%, 100%': {
                  'background-size': '200% 200%',
                  'background-position': 'left center'
                },
                '50%': {
                  'background-size': '200% 200%',
                  'background-position': 'right center'
                }
              }
            }
          }
        }
      }
    </script>

    <style>
        /* Enhanced TV Display Styles for DT */
        .market-header {
            background: linear-gradient(135deg, var(--primary-blue-dark) 0%, var(--primary-blue) 100%);
            color: white;
            padding: 2rem 0;
            box-shadow: 0 4px 20px rgba(30, 64, 175, 0.3);
        }

        .market-title {
            font-size: 2.5rem;
            font-weight: 800;
            text-align: center;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .market-date {
            color: #bfdbfe;
            font-weight: 600;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .metric-card {
            background: var(--glass-bg);
            backdrop-filter: blur(var(--glass-blur));
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-xl);
            padding: 2.5rem;
            text-align: center;
            transition: all var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-blue), var(--primary-blue-light));
        }

        .metric-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        .metric-label {
            font-size: 1rem;
            font-weight: 700;
            color: var(--neutral-gray);
            text-transform: uppercase;
            letter-spacing: 0.1em;
            margin-bottom: 1rem;
        }

        .metric-value {
            font-size: 3.5rem;
            font-weight: 900;
            color: var(--primary-blue);
            margin: 1rem 0;
            font-variant-numeric: tabular-nums;
        }

        .metric-unit {
            font-size: 1.1rem;
            color: var(--neutral-gray);
            font-weight: 600;
        }

        .chart-container {
            background: var(--glass-bg);
            backdrop-filter: blur(var(--glass-blur));
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-2xl);
            padding: 2.5rem;
            box-shadow: var(--glass-shadow);
        }

        .loading-spinner {
            display: inline-block;
            width: 2rem;
            height: 2rem;
            border: 3px solid rgba(59, 130, 246, 0.3);
            border-radius: 50%;
            border-top-color: var(--primary-blue);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .status-indicator {
            position: fixed;
            top: 6rem;
            right: 2rem;
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-lg);
            padding: 1rem 1.5rem;
            font-size: 0.9rem;
            font-weight: 600;
            z-index: 100;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-dot {
            width: 0.5rem;
            height: 0.5rem;
            border-radius: 50%;
            background: var(--secondary-green);
            animation: pulse 2s ease-in-out infinite;
        }
    </style>
</head>
<body>
    {% include 'partials/header.html' %}

    <!-- Status Indicator -->
    <div class="status-indicator" id="statusIndicator">
        <div class="status-dot"></div>
        <span>Načítání dat...</span>
    </div>

    <!-- Market Header -->
    <section class="market-header">
        <div class="max-w-7xl mx-auto px-8">
            <h1 class="market-title">
                Výsledky denního trhu ČR
                <br>
                <span class="market-date" id="market-date">Načítání...</span>
            </h1>
        </div>
    </section>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-8 py-12">
        <!-- Metrics Grid -->
        <div class="metrics-grid animate-stagger">
            <div class="metric-card">
                <div class="metric-label">Base Load</div>
                <div class="metric-value" id="base-load">
                    <div class="loading-spinner"></div>
                </div>
                <div class="metric-unit">EUR/MWh</div>
            </div>

            <div class="metric-card">
                <div class="metric-label">Peak Load</div>
                <div class="metric-value" id="peak-load">
                    <div class="loading-spinner"></div>
                </div>
                <div class="metric-unit">EUR/MWh</div>
            </div>

            <div class="metric-card">
                <div class="metric-label">Offpeak Load</div>
                <div class="metric-value" id="offpeak-load">
                    <div class="loading-spinner"></div>
                </div>
                <div class="metric-unit">EUR/MWh</div>
            </div>

            <div class="metric-card">
                <div class="metric-label">Denní množství</div>
                <div class="metric-value" id="total-load">
                    <div class="loading-spinner"></div>
                </div>
                <div class="metric-unit">MWh</div>
            </div>
        </div>

        <!-- Chart Container -->
        <div class="chart-container animate-fade-in-up">
            <div class="relative w-full h-[600px]">
                <canvas id="oteChart"></canvas>
            </div>
        </div>
    </main>

    <script>
        // Enhanced DateTime Update
        function updateDateTime() {
            const now = new Date();
            const options = {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            };
            const dateTimeElement = document.getElementById('dateTime');
            if (dateTimeElement) {
                dateTimeElement.textContent = now.toLocaleString('cs-CZ', options);
            }
        }

        // Enhanced Status Updates
        function updateStatus(message, isError = false) {
            const statusIndicator = document.getElementById('statusIndicator');
            const statusDot = statusIndicator.querySelector('.status-dot');
            const statusText = statusIndicator.querySelector('span');

            statusText.textContent = message;
            statusDot.style.background = isError ? '#dc2626' : '#059669';
        }

        // Enhanced Metric Updates with Animation
        function updateMetric(elementId, value, animate = true) {
            const element = document.getElementById(elementId);
            const spinner = element.querySelector('.loading-spinner');

            if (spinner) {
                spinner.remove();
            }

            if (animate && value !== '---') {
                // Animate number counting
                const numValue = parseFloat(value);
                if (!isNaN(numValue)) {
                    let current = 0;
                    const increment = numValue / 30;
                    const timer = setInterval(() => {
                        current += increment;
                        if (current >= numValue) {
                            element.textContent = numValue.toFixed(2);
                            clearInterval(timer);
                        } else {
                            element.textContent = current.toFixed(2);
                        }
                    }, 50);
                } else {
                    element.textContent = value;
                }
            } else {
                element.textContent = value;
            }
        }

        // Enhanced Data Fetching
        async function fetchDailyMarketData() {
            try {
                updateStatus('Načítání dat denního trhu...');

                const response = await fetch('/api/electricity-data');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();
                updateStatus('Data aktualizována');

                return data;
            } catch (error) {
                console.error('Error fetching daily market data:', error);
                updateStatus('Chyba při načítání dat', true);
                return null;
            }
        }

        let timeUpdateInterval;
        document.addEventListener('DOMContentLoaded', () => {
            updateDateTime();
            timeUpdateInterval = setInterval(updateDateTime, 1000);

            document.addEventListener('visibilitychange', () => {
                if (document.hidden) {
                    clearInterval(timeUpdateInterval);
                } else {
                    updateDateTime();
                    timeUpdateInterval = setInterval(updateDateTime, 1000);
                }
            });
        });
    </script>

    <script>
        // Enhanced Caching with Expiration
        function getCachedData(key, maxAge = 5 * 60 * 1000) { // 5 minutes default
            try {
                const cached = localStorage.getItem(key);
                if (cached) {
                    const data = JSON.parse(cached);
                    if (data.timestamp && (Date.now() - data.timestamp) < maxAge) {
                        return data.value;
                    }
                    localStorage.removeItem(key); // Remove expired data
                }
            } catch (error) {
                console.error('Error reading cache:', error);
                localStorage.removeItem(key);
            }
            return null;
        }

        function setCachedData(key, data) {
            try {
                const cacheData = {
                    value: data,
                    timestamp: Date.now()
                };
                localStorage.setItem(key, JSON.stringify(cacheData));
            } catch (error) {
                console.error('Error setting cache:', error);
            }
        }

        function getTodayDate() {
            return new Date().toISOString().split('T')[0];
        }

        // Enhanced IDA Summary Fetching
        async function fetchIdaSummary() {
            const date = getTodayDate();
            const cacheKey = `ida-summary-${date}`;
            const cachedData = getCachedData(cacheKey);

            if (cachedData) {
                return cachedData;
            }

            try {
                updateStatus('Načítání IDA dat...');
                const url = `/api/ida-data?ida_session=IDA1&report_date=${date}`;
                const response = await fetch(url);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();
                setCachedData(cacheKey, data);
                return data;
            } catch (error) {
                console.error('Error fetching IDA summary:', error);
                updateStatus('Chyba při načítání IDA dat', true);
                return null;
            }
        }

        // Enhanced Summary Cards Update
        async function updateSummaryCards() {
            try {
                const summary = await fetchIdaSummary();

                if (summary && summary.data && summary.data.length > 0) {
                    // Update market date
                    if (summary.used_date) {
                        const date = new Date(summary.used_date);
                        document.getElementById('market-date').textContent =
                            date.toLocaleDateString('cs-CZ');
                    }

                    // Update metrics with animation
                    for (const item of summary.data) {
                        if (item.index === 'BASE LOAD') {
                            updateMetric('base-load', item.price || '---');
                        } else if (item.index === 'PEAK LOAD') {
                            updateMetric('peak-load', item.price || '---');
                        } else if (item.index === 'OFFPEAK LOAD') {
                            updateMetric('offpeak-load', item.price || '---');
                        }
                    }

                    // Update total load
                    const totalValue = summary.total_amount !== null ?
                        summary.total_amount.toFixed(1) : '---';
                    updateMetric('total-load', totalValue);

                    updateStatus('Data aktualizována');
                } else {
                    // Fallback values
                    updateMetric('base-load', '---', false);
                    updateMetric('peak-load', '---', false);
                    updateMetric('offpeak-load', '---', false);
                    updateMetric('total-load', '---', false);
                    updateStatus('Žádná data k dispozici', true);
                }
            } catch (error) {
                console.error('Error updating summary cards:', error);
                updateStatus('Chyba při aktualizaci dat', true);
            }
        }

        // Enhanced Initialization
        window.onload = function() {
            console.log('DT Dashboard Enhanced - Initializing...');

            // Initial updates
            updateDateTime();
            updateSummaryCards();

            // Check if DTscript.js provides updateDashboard function
            if (typeof updateDashboard === 'function') {
                updateDashboard();
                setInterval(updateDashboard, 5 * 60 * 1000);
            }

            // Regular updates
            setInterval(updateSummaryCards, 5 * 60 * 1000);
            setInterval(() => {
                // Health check
                fetch('/api/health')
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'healthy') {
                            updateStatus('Systém v pořádku');
                        } else {
                            updateStatus('Systém degradován', true);
                        }
                    })
                    .catch(() => {
                        updateStatus('Chyba spojení', true);
                    });
            }, 30 * 1000); // Every 30 seconds

            console.log('DT Dashboard Enhanced - Initialized');
        };
    </script>

    <script src="{{ url_for('static', filename='js/enhanced-common.js') }}" defer></script>
    <script src="{{ url_for('static', filename='js/DTscript.js') }}" defer></script>
    <script src="{{ url_for('static', filename='js/dashboard.js') }}" defer></script>
</body>
</html>
