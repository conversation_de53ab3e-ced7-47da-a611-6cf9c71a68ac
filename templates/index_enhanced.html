<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTE Dashboard - Enhanced</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Styles -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enhanced-styles.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.0/chart.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <style>
        /* TV Display Specific Optimizations */
        .hero-section {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
            position: relative;
            overflow: hidden;
            min-height: 100vh;
        }
        
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(59, 130, 246, 0.2) 0%, transparent 50%);
            animation: float 20s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(1deg); }
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 1.5rem;
            padding: 2rem;
            text-align: center;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-blue), var(--primary-blue-light));
        }
        
        .stat-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }
        
        .stat-icon {
            width: 4rem;
            height: 4rem;
            border-radius: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 1.5rem;
            color: white;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }
        
        .stat-value {
            font-size: 3rem;
            font-weight: 900;
            margin: 1rem 0 0.5rem;
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-light));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .stat-label {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--neutral-gray);
            line-height: 1.4;
        }
        
        .timeline-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 1.5rem;
            padding: 2.5rem;
            margin-top: 2rem;
        }
        
        .timeline-item {
            display: flex;
            align-items: flex-start;
            gap: 1.5rem;
            padding: 1.5rem 0;
            border-bottom: 1px solid rgba(148, 163, 184, 0.2);
            transition: all 0.3s ease;
        }
        
        .timeline-item:last-child {
            border-bottom: none;
        }
        
        .timeline-item:hover {
            background: rgba(59, 130, 246, 0.05);
            border-radius: 1rem;
            padding-left: 1rem;
            padding-right: 1rem;
        }
        
        .timeline-dot {
            width: 1rem;
            height: 1rem;
            border-radius: 50%;
            flex-shrink: 0;
            margin-top: 0.25rem;
            box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
        }
        
        .timeline-content h4 {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--neutral-dark);
            margin: 0 0 0.5rem 0;
        }
        
        .timeline-content p {
            font-size: 1rem;
            color: var(--neutral-gray);
            margin: 0;
            line-height: 1.5;
        }
        
        /* Real-time data indicators */
        .data-status {
            position: fixed;
            top: 6rem;
            right: 2rem;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 1rem;
            padding: 1rem 1.5rem;
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--neutral-gray);
            z-index: 100;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .status-dot {
            width: 0.5rem;
            height: 0.5rem;
            border-radius: 50%;
            background: var(--secondary-green);
            animation: pulse 2s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.2); }
        }
        
        /* Performance optimizations for TV display */
        * {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        img {
            image-rendering: -webkit-optimize-contrast;
            image-rendering: crisp-edges;
        }
        
        /* Accessibility improvements */
        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
    </style>
</head>

<body>
    <!-- Enhanced Header -->
    <header class="enhanced-header">
        <div class="header-content">
            <div class="header-logo">
                <img src="{{ url_for('static', filename='OTE.png') }}" alt="OTE Logo">
                <div>
                    <h1 class="header-title">Operátor trhu s energiemi</h1>
                    <p style="margin: 0; opacity: 0.9; font-size: 1rem;">Spojujeme trhy a příležitosti</p>
                </div>
            </div>
            <div class="header-time" id="currentTime">
                {{ update_time }}
            </div>
        </div>
    </header>

    <!-- Progress Bar -->
    <div class="progress-bar-container">
        <div class="progress-bar" id="autoSwitchProgressBar"></div>
    </div>

    <!-- Data Status Indicator -->
    <div class="data-status" id="dataStatus">
        <div class="status-dot"></div>
        <span>Aktualizace dat...</span>
    </div>

    <!-- Main Content -->
    <main class="hero-section">
        <div class="max-w-7xl mx-auto px-8 py-12">
            <div class="grid lg:grid-cols-2 gap-12 items-start">
                
                <!-- Left Column: Main Content -->
                <div class="text-white space-y-8 animate-fade-in-up">
                    <div class="space-y-6">
                        <h1 class="text-6xl lg:text-7xl font-black leading-tight tracking-tight">
                            Energetické trhy
                            <span class="block text-blue-200">v reálném čase</span>
                        </h1>
                        <p class="text-xl lg:text-2xl text-blue-100 leading-relaxed max-w-2xl">
                            Profesionální dashboard pro monitoring výsledků obchodování 
                            s elektřinou a plynem na českém energetickém trhu.
                        </p>
                    </div>

                    <!-- Statistics Grid -->
                    <div class="stats-grid animate-stagger">
                        <div class="stat-card">
                            <div class="stat-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-value counter" data-target="90">0</div>
                            <div class="stat-label">Zaměstnanců</div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon" style="background: linear-gradient(135deg, #059669, #047857);">
                                <i class="fas fa-building"></i>
                            </div>
                            <div class="stat-value counter" data-target="34500">0</div>
                            <div class="stat-label">Registrovaných<br>účastníků trhu</div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon" style="background: linear-gradient(135deg, #d97706, #b45309);">
                                <i class="fas fa-industry"></i>
                            </div>
                            <div class="stat-value counter" data-target="36600">0</div>
                            <div class="stat-label">Výrobních zdrojů<br>energie</div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon" style="background: linear-gradient(135deg, #7c3aed, #5b21b6);">
                                <i class="fas fa-bolt"></i>
                            </div>
                            <div class="stat-value counter" data-target="24">0</div>
                            <div class="stat-label">Let na trhu</div>
                        </div>
                    </div>
                </div>

                <!-- Right Column: Timeline -->
                <div class="timeline-section animate-slide-in-right">
                    <h3 class="text-2xl font-bold text-gray-800 mb-6">Klíčové milníky</h3>
                    
                    <div class="space-y-0">
                        <div class="timeline-item">
                            <div class="timeline-dot" style="background: #3b82f6;"></div>
                            <div class="timeline-content">
                                <h4>2002 - Elektřina</h4>
                                <p>Zahájení činnosti operátora trhu s elektřinou</p>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="timeline-dot" style="background: #059669;"></div>
                            <div class="timeline-content">
                                <h4>2004 - Emisní povolenky</h4>
                                <p>Spuštění národního registru emisních povolenek</p>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="timeline-dot" style="background: #d97706;"></div>
                            <div class="timeline-content">
                                <h4>2010 - Plyn</h4>
                                <p>Rozšíření činnosti na trh s plynem</p>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="timeline-dot" style="background: #7c3aed;"></div>
                            <div class="timeline-content">
                                <h4>2013 - Záruky původu</h4>
                                <p>Implementace systému záruk původu elektřiny z OZE</p>
                            </div>
                        </div>
                        
                        <div class="timeline-item" style="background: rgba(59, 130, 246, 0.1); border-radius: 1rem; padding: 1.5rem;">
                            <div class="timeline-dot" style="background: #1e40af; box-shadow: 0 0 0 4px rgba(30, 64, 175, 0.3);"></div>
                            <div class="timeline-content">
                                <h4 style="color: #1e40af;">2023 - Kapitál 1 mld Kč</h4>
                                <p style="color: #1e40af; font-weight: 600;">Navýšení základního kapitálu na 1 miliardu korun</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Enhanced JavaScript -->
    <script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>
    <script>
        // Enhanced counter animation
        function animateCounter(element, target, duration = 2000) {
            let current = 0;
            const increment = target / (duration / 16);
            
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    element.textContent = target.toLocaleString('cs-CZ');
                    clearInterval(timer);
                } else {
                    element.textContent = Math.floor(current).toLocaleString('cs-CZ');
                }
            }, 16);
        }
        
        // Real-time clock
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('cs-CZ', {
                day: '2-digit',
                month: '2-digit', 
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('currentTime').textContent = timeString;
        }
        
        // Data status monitoring
        async function updateDataStatus() {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                const statusElement = document.getElementById('dataStatus');
                const statusDot = statusElement.querySelector('.status-dot');
                const statusText = statusElement.querySelector('span');
                
                if (data.status === 'healthy') {
                    statusDot.style.background = '#059669';
                    statusText.textContent = 'Data aktuální';
                } else {
                    statusDot.style.background = '#d97706';
                    statusText.textContent = 'Aktualizace dat...';
                }
            } catch (error) {
                console.error('Error checking data status:', error);
            }
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Animate counters
            document.querySelectorAll('.counter').forEach(counter => {
                const target = parseInt(counter.getAttribute('data-target'));
                setTimeout(() => animateCounter(counter, target), 500);
            });
            
            // Start real-time updates
            updateTime();
            setInterval(updateTime, 1000);
            
            updateDataStatus();
            setInterval(updateDataStatus, 30000); // Check every 30 seconds
            
            console.log('OTE Dashboard Enhanced initialized');
        });
    </script>
</body>
</html>
