/* Enhanced OTE Dashboard Styles - TV Display Optimized */

/* CSS Custom Properties for TV Display */
:root {
  /* TV Display Scaling */
  --tv-font-scale: 1.4;
  --tv-line-height: 1.6;
  --tv-letter-spacing: 0.02em;
  
  /* Professional Corporate Colors */
  --primary-blue: #1e40af;
  --primary-blue-light: #3b82f6;
  --primary-blue-dark: #1e3a8a;
  --secondary-green: #059669;
  --accent-amber: #d97706;
  --neutral-gray: #64748b;
  --neutral-light: #f1f5f9;
  --neutral-dark: #0f172a;
  
  /* Glass Morphism */
  --glass-bg: rgba(255, 255, 255, 0.95);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  --glass-blur: 16px;
  
  /* Spacing Scale */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;
  
  /* Animation Timing */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.3s ease-out;
  --transition-slow: 0.6s ease-out;
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
}

/* Base Styles - TV Optimized */
* {
  box-sizing: border-box;
}

html {
  font-size: calc(16px * var(--tv-font-scale));
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 500;
  line-height: var(--tv-line-height);
  letter-spacing: var(--tv-letter-spacing);
  color: var(--neutral-dark);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  margin: 0;
  padding: 0;
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Enhanced Typography for TV Display */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  margin: 0 0 var(--space-md) 0;
  color: var(--neutral-dark);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1.125rem; }

p {
  margin: 0 0 var(--space-md) 0;
  font-size: 1.1rem;
}

/* Enhanced Glass Morphism Cards */
.glass-card {
  background: var(--glass-bg);
  backdrop-filter: blur(var(--glass-blur));
  -webkit-backdrop-filter: blur(var(--glass-blur));
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  box-shadow: var(--glass-shadow);
  padding: var(--space-xl);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.glass-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
}

.glass-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

/* Professional Data Cards */
.data-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  box-shadow: 
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.data-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, var(--primary-blue), var(--primary-blue-light));
  border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
}

.data-card:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Enhanced Metrics Display */
.metric-value {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--primary-blue);
  margin: var(--space-sm) 0;
  font-variant-numeric: tabular-nums;
  letter-spacing: -0.02em;
}

.metric-label {
  font-size: 1rem;
  font-weight: 600;
  color: var(--neutral-gray);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-xs);
}

.metric-change {
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  font-size: 0.9rem;
  font-weight: 600;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  margin-top: var(--space-sm);
}

.metric-change.positive {
  color: var(--secondary-green);
  background: rgba(5, 150, 105, 0.1);
}

.metric-change.negative {
  color: #dc2626;
  background: rgba(220, 38, 38, 0.1);
}

/* Enhanced Grid Layouts */
.dashboard-grid {
  display: grid;
  gap: var(--space-xl);
  padding: var(--space-xl);
  max-width: 1400px;
  margin: 0 auto;
}

.grid-2 { grid-template-columns: repeat(2, 1fr); }
.grid-3 { grid-template-columns: repeat(3, 1fr); }
.grid-4 { grid-template-columns: repeat(4, 1fr); }

@media (max-width: 1200px) {
  .grid-4 { grid-template-columns: repeat(2, 1fr); }
  .grid-3 { grid-template-columns: repeat(2, 1fr); }
}

@media (max-width: 768px) {
  .grid-2, .grid-3, .grid-4 { 
    grid-template-columns: 1fr; 
  }
}

/* Enhanced Header */
.enhanced-header {
  background: linear-gradient(135deg, var(--primary-blue-dark) 0%, var(--primary-blue) 50%, var(--primary-blue-light) 100%);
  color: white;
  padding: var(--space-lg) 0;
  box-shadow: 0 4px 20px rgba(30, 64, 175, 0.3);
  position: sticky;
  top: 0;
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--space-xl);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-logo {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.header-logo img {
  height: 3rem;
  transition: transform var(--transition-normal);
}

.header-logo img:hover {
  transform: scale(1.05);
}

.header-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
}

.header-time {
  font-size: 1.25rem;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.2);
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  font-variant-numeric: tabular-nums;
}

/* Enhanced Progress Bar */
.progress-bar-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: rgba(0, 0, 0, 0.1);
  z-index: 1001;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-blue), var(--accent-amber));
  transition: width 1s linear;
  box-shadow: 0 0 10px rgba(30, 64, 175, 0.5);
}

/* Enhanced Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.8s ease-out;
}

.animate-pulse-slow {
  animation: pulse 3s ease-in-out infinite;
}

/* Staggered animations */
.animate-stagger > * {
  opacity: 0;
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-stagger > *:nth-child(1) { animation-delay: 0.1s; }
.animate-stagger > *:nth-child(2) { animation-delay: 0.2s; }
.animate-stagger > *:nth-child(3) { animation-delay: 0.3s; }
.animate-stagger > *:nth-child(4) { animation-delay: 0.4s; }
.animate-stagger > *:nth-child(5) { animation-delay: 0.5s; }
.animate-stagger > *:nth-child(6) { animation-delay: 0.6s; }
