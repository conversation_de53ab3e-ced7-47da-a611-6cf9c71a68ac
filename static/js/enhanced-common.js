/**
 * Enhanced Common JavaScript for OTE Dashboard
 * Shared functionality across all pages with TV display optimizations
 */

// Global configuration
const OTE_CONFIG = {
    UPDATE_INTERVAL: 5 * 60 * 1000, // 5 minutes
    CACHE_DURATION: 5 * 60 * 1000,  // 5 minutes
    HEALTH_CHECK_INTERVAL: 30 * 1000, // 30 seconds
    ANIMATION_DURATION: 50, // milliseconds for counter animations
    CHART_COLORS: {
        primary: '#3b82f6',
        secondary: '#1d4ed8',
        success: '#059669',
        warning: '#d97706',
        danger: '#dc2626'
    }
};

// Enhanced DateTime Management
class DateTimeManager {
    constructor() {
        this.interval = null;
        this.isVisible = true;
        this.init();
    }

    init() {
        this.updateDateTime();
        this.startInterval();
        this.setupVisibilityHandler();
    }

    updateDateTime() {
        const now = new Date();
        const options = {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        };
        
        const dateTimeElement = document.getElementById('dateTime');
        if (dateTimeElement) {
            dateTimeElement.textContent = now.toLocaleString('cs-CZ', options);
        }
    }

    startInterval() {
        if (this.interval) clearInterval(this.interval);
        this.interval = setInterval(() => this.updateDateTime(), 1000);
    }

    setupVisibilityHandler() {
        document.addEventListener('visibilitychange', () => {
            this.isVisible = !document.hidden;
            if (this.isVisible) {
                this.updateDateTime();
                this.startInterval();
            } else {
                if (this.interval) clearInterval(this.interval);
            }
        });
    }
}

// Enhanced Caching System
class CacheManager {
    static getCachedData(key, maxAge = OTE_CONFIG.CACHE_DURATION) {
        try {
            const cached = localStorage.getItem(key);
            if (cached) {
                const data = JSON.parse(cached);
                if (data.timestamp && (Date.now() - data.timestamp) < maxAge) {
                    return data.value;
                }
                localStorage.removeItem(key);
            }
        } catch (error) {
            console.error('Error reading cache:', error);
            localStorage.removeItem(key);
        }
        return null;
    }

    static setCachedData(key, data) {
        try {
            const cacheData = {
                value: data,
                timestamp: Date.now()
            };
            localStorage.setItem(key, JSON.stringify(cacheData));
        } catch (error) {
            console.error('Error setting cache:', error);
        }
    }

    static clearExpiredCache() {
        const keys = Object.keys(localStorage);
        keys.forEach(key => {
            if (key.startsWith('ote_')) {
                this.getCachedData(key); // This will remove expired items
            }
        });
    }
}

// Enhanced Status Management
class StatusManager {
    constructor() {
        this.statusElement = null;
        this.statusDot = null;
        this.statusText = null;
        this.init();
    }

    init() {
        this.statusElement = document.getElementById('statusIndicator');
        if (this.statusElement) {
            this.statusDot = this.statusElement.querySelector('.status-dot');
            this.statusText = this.statusElement.querySelector('span');
        }
    }

    updateStatus(message, isError = false) {
        if (this.statusText) {
            this.statusText.textContent = message;
        }
        if (this.statusDot) {
            this.statusDot.style.background = isError ? 
                OTE_CONFIG.CHART_COLORS.danger : 
                OTE_CONFIG.CHART_COLORS.success;
        }
    }

    showLoading(message = 'Načítání dat...') {
        this.updateStatus(message, false);
    }

    showError(message = 'Chyba při načítání dat') {
        this.updateStatus(message, true);
    }

    showSuccess(message = 'Data aktualizována') {
        this.updateStatus(message, false);
    }
}

// Enhanced Metric Updates with Animation
class MetricManager {
    static updateMetric(elementId, value, animate = true) {
        const element = document.getElementById(elementId);
        if (!element) return;

        const spinner = element.querySelector('.loading-spinner');
        if (spinner) {
            spinner.remove();
        }

        if (animate && value !== '---' && !isNaN(parseFloat(value))) {
            this.animateValue(element, parseFloat(value));
        } else {
            element.textContent = value;
        }
    }

    static animateValue(element, targetValue) {
        let current = 0;
        const increment = targetValue / 30;
        const timer = setInterval(() => {
            current += increment;
            if (current >= targetValue) {
                element.textContent = targetValue.toFixed(2);
                clearInterval(timer);
            } else {
                element.textContent = current.toFixed(2);
            }
        }, OTE_CONFIG.ANIMATION_DURATION);
    }

    static setLoadingState(elementId) {
        const element = document.getElementById(elementId);
        if (element && !element.querySelector('.loading-spinner')) {
            element.innerHTML = '<div class="loading-spinner"></div>';
        }
    }
}

// Enhanced API Client
class APIClient {
    static async fetchWithCache(url, cacheKey, maxAge = OTE_CONFIG.CACHE_DURATION) {
        // Try cache first
        const cachedData = CacheManager.getCachedData(cacheKey, maxAge);
        if (cachedData) {
            return cachedData;
        }

        // Fetch from API
        try {
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            CacheManager.setCachedData(cacheKey, data);
            return data;
        } catch (error) {
            console.error(`Error fetching ${url}:`, error);
            throw error;
        }
    }

    static async healthCheck() {
        try {
            const response = await fetch('/api/health');
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Health check failed:', error);
            return { status: 'error', error: error.message };
        }
    }
}

// Enhanced Chart Utilities
class ChartUtils {
    static getDefaultOptions(title = '') {
        return {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: !!title,
                    text: title,
                    font: {
                        size: 18,
                        weight: 'bold'
                    },
                    color: '#1f2937'
                },
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        font: {
                            size: 14,
                            weight: '600'
                        },
                        color: '#374151'
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        font: {
                            size: 12,
                            weight: '500'
                        },
                        color: '#6b7280'
                    }
                },
                y: {
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        font: {
                            size: 12,
                            weight: '500'
                        },
                        color: '#6b7280'
                    }
                }
            },
            animation: {
                duration: 1000,
                easing: 'easeInOutQuart'
            }
        };
    }

    static formatHourLabels(hours) {
        return hours.map(hour => {
            const h = Math.floor(hour / 4);
            const m = (hour % 4) * 15;
            return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}`;
        });
    }
}

// Global instances
let dateTimeManager;
let statusManager;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('OTE Dashboard Enhanced Common - Initializing...');
    
    // Initialize managers
    dateTimeManager = new DateTimeManager();
    statusManager = new StatusManager();
    
    // Clear expired cache
    CacheManager.clearExpiredCache();
    
    // Setup periodic health checks
    setInterval(async () => {
        try {
            const health = await APIClient.healthCheck();
            if (health.status === 'healthy') {
                statusManager.showSuccess('Systém v pořádku');
            } else {
                statusManager.showError('Systém degradován');
            }
        } catch (error) {
            statusManager.showError('Chyba spojení');
        }
    }, OTE_CONFIG.HEALTH_CHECK_INTERVAL);
    
    console.log('OTE Dashboard Enhanced Common - Initialized');
});

// Export for use in other scripts
window.OTE = {
    CONFIG: OTE_CONFIG,
    DateTimeManager,
    CacheManager,
    StatusManager,
    MetricManager,
    APIClient,
    ChartUtils,
    statusManager,
    dateTimeManager
};
