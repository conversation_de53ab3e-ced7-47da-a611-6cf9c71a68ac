class GlobalDashboardManager {
  constructor() {
    this.progressBar = document.getElementById('autoSwitchProgressBar');
    this.progressBarWrapper = document.getElementById('autoSwitchProgressBarWrapper');
    this.autoSwitchDuration = 30; // 30 seconds for TV display
    this.autoSwitchTimer = null;
    this.autoSwitchElapsed = 0;
    this.isTransitioning = false;
    this.isPaused = false;
    this.rotationSequence = [
      { url: '/', name: '<PERSON><PERSON><PERSON><PERSON> strán<PERSON>' },
      { url: '/dt', name: 'Denn<PERSON> trh' },
      { url: '/vdt-kon', name: 'VDT Kontinuální' },
      { url: '/ida1', name: 'IDA1' },
      { url: '/ida2', name: 'IDA2' },
      { url: '/ida3', name: 'IDA3' },
      { url: '/vdt-p', name: 'VDT-P' }
    ];
    this.setupInteractionHandlers();
    this.startAutoSwitchCountdown();
    this.initializeDateTime();
  }

  setupInteractionHandlers() {
    // Pause auto-switch on user interaction
    const interactionEvents = ['click', 'keydown', 'mousemove', 'scroll', 'touchstart'];
    let interactionTimer;

    const pauseAutoSwitch = () => {
      if (interactionTimer) clearTimeout(interactionTimer);
      this.pauseAutoSwitch();

      // Resume after 10 seconds of no interaction
      interactionTimer = setTimeout(() => {
        this.resumeAutoSwitch();
      }, 10000);
    };

    interactionEvents.forEach(event => {
      document.addEventListener(event, pauseAutoSwitch, { passive: true });
    });

    // Handle visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.pauseAutoSwitch();
      } else {
        this.resumeAutoSwitch();
      }
    });
  }

  startAutoSwitchCountdown() {
    if (!this.progressBar || !this.progressBarWrapper || this.isPaused) return;

    this.progressBarWrapper.style.display = '';
    this.progressBar.style.width = '100%';
    this.autoSwitchElapsed = 0;

    if (this.autoSwitchTimer) clearInterval(this.autoSwitchTimer);

    this.autoSwitchTimer = setInterval(() => {
      if (this.isPaused) return;

      this.autoSwitchElapsed++;
      const percent = Math.max(0, 100 - (this.autoSwitchElapsed / this.autoSwitchDuration) * 100);
      this.progressBar.style.width = percent + '%';

      if (this.autoSwitchElapsed >= this.autoSwitchDuration) {
        this.handleRotation();
        this.resetProgressBar();
      }
    }, 1000);
  }

  pauseAutoSwitch() {
    this.isPaused = true;
    if (this.autoSwitchTimer) {
      clearInterval(this.autoSwitchTimer);
    }
  }

  resumeAutoSwitch() {
    this.isPaused = false;
    this.startAutoSwitchCountdown();
  }

  resetProgressBar() {
    this.autoSwitchElapsed = 0;
    this.progressBar.style.width = '100%';
  }

  getCurrentIndex() {
    let path = window.location.pathname.replace(/\.html$/, '');
    if (path === '' || path === '/') path = '/';
    for (let i = 0; i < this.rotationSequence.length; i++) {
      if (this.rotationSequence[i].url === path) {
        return i;
      }
    }
    return 0;
  }

  handleRotation() {
    if (this.isTransitioning) return;

    this.isTransitioning = true;
    const currentIndex = this.getCurrentIndex();
    const nextIndex = (currentIndex + 1) % this.rotationSequence.length;
    const next = this.rotationSequence[nextIndex];

    // Add smooth transition effect
    document.body.style.opacity = '0.8';
    document.body.style.transition = 'opacity 0.3s ease-out';

    setTimeout(() => {
      window.location.href = next.url;
    }, 300);
  }

  initializeDateTime() {
    const updateDateTime = () => {
      const now = new Date();
      const options = {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      };
      const dateTimeElement = document.getElementById('dateTime');
      if (dateTimeElement) {
        dateTimeElement.textContent = now.toLocaleString('cs-CZ', options);
      }
    };
    updateDateTime();
    setInterval(updateDateTime, 1000);
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) updateDateTime();
    });
  }
}

document.addEventListener('DOMContentLoaded', () => {
  new GlobalDashboardManager();
});

setTimeout(function() {
  window.location.reload();
}, 30 * 60 * 1000);

document.addEventListener('DOMContentLoaded', function() {
  const animateElements = document.querySelectorAll('.tv-card, .timeline-item');

  animateElements.forEach((element, index) => {
    setTimeout(() => {
      element.style.opacity = '1';
      element.style.transform = 'translateY(0)';
    }, 100 * index);
  });
});
