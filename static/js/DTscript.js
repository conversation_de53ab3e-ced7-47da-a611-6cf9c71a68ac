function updateDateTime() {
    const now = new Date();
    document.getElementById('dateTime').textContent = now.toLocaleString('cs-CZ', {
        day: '2-digit', month: '2-digit', year: 'numeric',
        hour: '2-digit', minute: '2-digit', second: '2-digit'
    });
}

setInterval(updateDateTime, 1000);

async function fetchData() {
    const today = new Date().toISOString().split('T')[0];

    // Try OTE website first
    const oteUrl = `https://www.ote-cr.cz/cs/kratkodobe-trhy/elektrina/denni-trh/@@chart-data?report_date=`;

    try {
        const response = await fetch(oteUrl);
        if (!response.ok) {
            throw new Error('Chyba při načítání dat z OTE URL: ' + response.statusText);
        }
        const data = await response.json();
        console.log("Data úspěšně načtena z OTE URL:", data);
        return data;
    } catch (error) {
        console.error("Chyba při načítání dat z OTE URL:", error);

        // Fallback to our API
        try {
            const apiResponse = await fetch('/api/electricity-data');
            if (apiResponse.ok) {
                const apiData = await apiResponse.json();
                console.log("Data načtena z API:", apiData);

                // Convert API data to chart format if needed
                if (apiData.data && Array.isArray(apiData.data)) {
                    return {
                        data: {
                            dataLine: [
                                { point: apiData.data.map((item, index) => ({ x: index, y: parseFloat(item.price) || 0 })) }
                            ]
                        },
                        graph: { title: "Denní trh - API data" }
                    };
                }
            }
        } catch (apiError) {
            console.error("Chyba při načítání dat z API:", apiError);
        }

        return null;
    }
}

function updateMetric(id, value) {
    const element = document.getElementById(id);
    if (element) {
        // Remove loading spinner if present
        const spinner = element.querySelector('.loading-spinner');
        if (spinner) {
            spinner.remove();
        }
        element.textContent = value !== undefined ? value : '---';
    }
}

function createChart(canvasId, chartData) {
    const ctx = document.getElementById(canvasId).getContext('2d');

    if (window.myChart) {
        window.myChart.destroy();
    }

    window.myChart = new Chart(ctx, {
        data: {
            labels: chartData.labels,
            datasets: [
                {
                    label: 'Cena (EUR/MWh)',
                    data: chartData.cena,
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    borderWidth: 2,
                    pointRadius: 4,
                    pointBackgroundColor: 'white',
                    pointBorderColor: 'rgb(255, 99, 132)',
                    type: 'line',
                    yAxisID: 'y-axis-1'
                },
                {
                    label: 'Množství (MWh)',
                    data: chartData.mnozstvi,
                    backgroundColor: 'rgba(54, 162, 235, 0.8)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 2,
                    type: 'bar',
                    yAxisID: 'y-axis-2'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    title: {
                        display: true,
                        text: 'Hodina',
                        color: '#333'
                    },
                    grid: {
                        display: true,
                        color: '#e0e0e0'
                    },
                    ticks: {
                        color: '#333'
                    }
                },
                'y-axis-1': {
                    type: 'linear',
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Cena (EUR/MWh)',
                        color: '#333'
                    },
                    beginAtZero: true,
                    grid: {
                        display: true,
                        color: '#e0e0e0'
                    },
                    ticks: {
                        color: '#333'
                    }
                },
                'y-axis-2': {
                    type: 'linear',
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Množství (MWh)',
                        color: '#333'
                    },
                    beginAtZero: true,
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#333'
                    }
                }
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        color: '#333',
                        boxWidth: 25,
                        boxHeight: 25,
                        padding: 25,
                        usePointStyle: false,
                        generateLabels: function (chart) {
                            const labels = Chart.defaults.plugins.legend.labels.generateLabels(chart);
                            labels.forEach((label, index) => {
                                if (index === 0) {
                                    label.fillStyle = 'rgba(255, 99, 132, 0.5)';
                                    label.strokeStyle = 'rgb(255, 99, 132)';
                                    label.lineWidth = 2;
                                } else if (index === 1) {
                                    label.fillStyle = 'rgba(54, 162, 235, 0.8)';
                                    label.strokeStyle = 'rgba(54, 162, 235, 1)';
                                    label.lineWidth = 2;
                                }
                            });
                            return labels;
                        }
                    },
                    onClick: function (e, legendItem, legend) {
                        const index = legendItem.datasetIndex;
                        const ci = legend.chart;
                        const meta = ci.getDatasetMeta(index);

                        meta.hidden = meta.hidden === null ? !ci.data.datasets[index].hidden : null;
                        ci.update();
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: '#ddd',
                    borderWidth: 1,
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            if (context.parsed.y !== null) {
                                label += context.parsed.y.toFixed(2);
                            }
                            return label;
                        }
                    }
                }
            }
        }
    });
}

async function updateDashboard() {
    console.log("updateDashboard called");
    const data = await fetchData();

    if (data) {
        const { dataLine } = data.data;
        const graphTitle = data.graph ? data.graph.title : '';
        const dateMatch = graphTitle.match(/\d{2}\.\d{2}\.\d{4}/);

        // Update market date if element exists
        const marketDateElement = document.getElementById('market-date');
        if (marketDateElement) {
            if (dateMatch) {
                marketDateElement.textContent = dateMatch[0];
            } else {
                const today = new Date().toLocaleDateString('cs-CZ');
                marketDateElement.textContent = today;
            }
        }

        if (dataLine && dataLine.length > 0) {
            // Create chart data
            const chartData = {
                labels: dataLine[0].point.map((p, index) => {
                    // Convert index to hour format
                    const hour = Math.floor(index / 4);
                    const minute = (index % 4) * 15;
                    return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
                }),
                mnozstvi: dataLine[0].point.map(p => p.y),
                cena: dataLine.length > 1 ? dataLine[1].point.map(p => p.y) : dataLine[0].point.map(p => p.y)
            };

            createChart('oteChart', chartData);

            // Calculate total load
            const totalLoad = dataLine[0].point.reduce((sum, p) => sum + (typeof p.y === 'number' ? p.y : 0), 0);
            const totalLoadElement = document.getElementById('total-load');
            if (totalLoadElement) {
                totalLoadElement.textContent = totalLoad ? totalLoad.toLocaleString('cs-CZ', {maximumFractionDigits: 1}) : '---';
            }

            // Update metrics if elements exist
            updateMetric('base-load', '---');
            updateMetric('peak-load', '---');
            updateMetric('offpeak-load', '---');
        }
    } else {
        console.error("Nepodařilo se načíst data z API.");
        const marketDateElement = document.getElementById('market-date');
        if (marketDateElement) {
            marketDateElement.textContent = 'Chyba při načítání dat';
        }

        // Set loading state for metrics
        updateMetric('base-load', '---');
        updateMetric('peak-load', '---');
        updateMetric('offpeak-load', '---');
        updateMetric('total-load', '---');
    }
}

window.onload = function() {
    updateDashboard();
    updateDateTime();
    setInterval(updateDashboard, 5 * 60 * 1000);
};

let chart;

async function fetchAndDisplayDT() {
    const cacheKey = 'dtApiData';
    const cacheTimeKey = 'dtApiDataTime';
    const cacheDuration = 5 * 60 * 1000; // 5 minut
    const cachedData = localStorage.getItem(cacheKey);
    const cachedTime = localStorage.getItem(cacheTimeKey);
    let data;
    if (cachedData && cachedTime && (Date.now() - cachedTime < cacheDuration)) {
        data = JSON.parse(cachedData);
    } else {
        try {
            const resp = await fetch('/api/electricity-data');
            const json = await resp.json();
            data = json.data;
            localStorage.setItem(cacheKey, JSON.stringify(data));
            localStorage.setItem(cacheTimeKey, Date.now());
        } catch (e) {
            data = null;
        }
    }
    try {
        const base = data?.find(item => item.commodity && item.commodity.toUpperCase().includes('BASE LOAD'));
        const peak = data?.find(item => item.commodity && item.commodity.toUpperCase().includes('PEAK LOAD'));
        const offpeak = data?.find(item => item.commodity && item.commodity.toUpperCase().includes('OFFPEAK LOAD'));
        document.getElementById('base-load').textContent = base?.price || '-';
        document.getElementById('peak-load').textContent = peak?.price || '-';
        document.getElementById('offpeak-load').textContent = offpeak?.price || '-';
    } catch (e) {
        document.getElementById('base-load').textContent = '-';
        document.getElementById('peak-load').textContent = '-';
        document.getElementById('offpeak-load').textContent = '-';
    }
}

window.addEventListener('DOMContentLoaded', () => {
    fetchAndDisplayDT();
    setInterval(fetchAndDisplayDT, 5 * 60 * 1000);
});
