#!/bin/bash

echo "🔧 OTE Dashboard - Starting Fixed Version"
echo "=========================================="

# Kill any existing processes on port 8085
echo "Stopping existing processes..."
lsof -ti:8085 | xargs kill -9 2>/dev/null || true

# Wait a moment
sleep 2

# Start the fixed version
echo "Starting OTE Dashboard Fixed Version on port 8085..."
python3 -c "
import sys
sys.path.insert(0, '.')

# Modify the fixed app to run on port 8085
with open('app_fixed.py', 'r') as f:
    content = f.read()

# Replace port 8086 with 8085
content = content.replace('port=8086', 'port=8085')

# Write to temporary file and run
with open('app_temp.py', 'w') as f:
    f.write(content)

exec(open('app_temp.py').read())
"
