#!/usr/bin/env python3
"""
Enhanced OTE Dashboard Startup Script
Optimized for Raspberry Pi deployment with performance monitoring
"""

import os
import sys
import logging
import signal
import time
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

def setup_logging():
    """Configure logging for production"""
    log_level = logging.INFO if not os.getenv('DEBUG') else logging.DEBUG
    
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('ote_dashboard.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # Reduce noise from external libraries
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('werkzeug').setLevel(logging.WARNING)

def check_dependencies():
    """Check if all required dependencies are installed"""
    required_packages = [
        ('flask', 'flask'),
        ('redis', 'redis'),
        ('aiohttp', 'aiohttp'),
        ('beautifulsoup4', 'bs4'),
        ('requests', 'requests'),
        ('psutil', 'psutil'),
        ('flask-cors', 'flask_cors'),
        ('flask-caching', 'flask_caching')
    ]

    missing_packages = []
    for package_name, import_name in required_packages:
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)
    
    if missing_packages:
        print(f"Missing required packages: {', '.join(missing_packages)}")
        print("Please install them using: pip install -r requirements.txt")
        return False
    
    return True

def check_redis_connection():
    """Check if Redis is available"""
    try:
        import redis
        redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
        client = redis.from_url(redis_url, decode_responses=True)
        client.ping()
        print("✓ Redis connection successful")
        return True
    except Exception as e:
        print(f"⚠ Redis not available: {e}")
        print("  Application will use in-memory caching as fallback")
        return False

def setup_environment():
    """Setup environment variables with defaults"""
    env_defaults = {
        'FLASK_ENV': 'production',
        'CACHE_TIMEOUT': '900',  # 15 minutes
        'REQUEST_TIMEOUT': '10',
        'MAX_CONNECTIONS': '20',
        'UPDATE_INTERVAL': '300',  # 5 minutes
        'HOST': '0.0.0.0',
        'PORT': '8085',
        'WORKERS': '2',  # Optimized for Raspberry Pi
    }
    
    for key, default_value in env_defaults.items():
        if key not in os.environ:
            os.environ[key] = default_value

def check_system_resources():
    """Check if system has sufficient resources"""
    try:
        import psutil
        
        # Check available memory
        memory = psutil.virtual_memory()
        available_mb = memory.available // (1024 * 1024)
        
        if available_mb < 100:  # Less than 100MB available
            print(f"⚠ Low memory warning: {available_mb}MB available")
            print("  Consider closing other applications")
        else:
            print(f"✓ Memory check passed: {available_mb}MB available")
        
        # Check CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        if cpu_percent > 80:
            print(f"⚠ High CPU usage: {cpu_percent}%")
        else:
            print(f"✓ CPU check passed: {cpu_percent}% usage")
        
        return True
        
    except ImportError:
        print("⚠ Cannot check system resources (psutil not available)")
        return True

def create_systemd_service():
    """Create systemd service file for auto-start"""
    service_content = f"""[Unit]
Description=OTE Dashboard Enhanced
After=network.target

[Service]
Type=simple
User=pi
WorkingDirectory={Path(__file__).parent.absolute()}
Environment=PATH={Path(__file__).parent.absolute()}
ExecStart=/usr/bin/python3 {Path(__file__).absolute()}
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""
    
    service_path = Path('/etc/systemd/system/ote-dashboard.service')
    
    try:
        if os.geteuid() == 0:  # Running as root
            service_path.write_text(service_content)
            os.system('systemctl daemon-reload')
            os.system('systemctl enable ote-dashboard.service')
            print(f"✓ Systemd service created: {service_path}")
            print("  Use 'sudo systemctl start ote-dashboard' to start")
            print("  Use 'sudo systemctl status ote-dashboard' to check status")
        else:
            print("ℹ To create systemd service, run as root:")
            print(f"  sudo python3 {__file__} --create-service")
    except Exception as e:
        print(f"⚠ Could not create systemd service: {e}")

def signal_handler(signum, frame):
    """Handle shutdown signals gracefully"""
    print(f"\nReceived signal {signum}, shutting down gracefully...")
    sys.exit(0)

def main():
    """Main startup function"""
    print("=" * 60)
    print("OTE Dashboard Enhanced - Starting Up")
    print("=" * 60)
    
    # Handle command line arguments
    if len(sys.argv) > 1:
        if '--create-service' in sys.argv:
            create_systemd_service()
            return
        elif '--check' in sys.argv:
            print("Running system checks...")
            check_dependencies()
            check_redis_connection()
            check_system_resources()
            print("System check completed.")
            return
    
    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)
    
    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Setup environment
    setup_environment()
    
    # Run system checks
    print("Running pre-flight checks...")
    
    if not check_dependencies():
        sys.exit(1)
    
    check_redis_connection()
    check_system_resources()
    
    print("✓ All checks passed")
    print()
    
    # Import and start the application
    try:
        from app_enhanced import app
        
        host = os.getenv('HOST', '0.0.0.0')
        port = int(os.getenv('PORT', '8085'))
        debug = os.getenv('DEBUG', 'False').lower() == 'true'
        
        print(f"Starting OTE Dashboard Enhanced...")
        print(f"  Host: {host}")
        print(f"  Port: {port}")
        print(f"  Debug: {debug}")
        print(f"  Environment: {os.getenv('FLASK_ENV', 'production')}")
        print()
        print(f"Dashboard will be available at: http://{host}:{port}")
        print("Press Ctrl+C to stop")
        print("=" * 60)
        
        # Start the Flask application
        app.run(
            host=host,
            port=port,
            debug=debug,
            threaded=True,
            use_reloader=False  # Disable reloader in production
        )
        
    except ImportError as e:
        logger.error(f"Failed to import application: {e}")
        print("Make sure app_enhanced.py is in the same directory")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
